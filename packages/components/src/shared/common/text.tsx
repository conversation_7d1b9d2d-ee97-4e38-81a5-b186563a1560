import clsx from 'clsx'
import type { FC } from 'react'

export interface TextStylesProps {
  variant?:
    | 'dim'
    | 'dim-xs'
    | 'dimmer'
    | 'subtitle'
    | 'slide'
    | 'dim-2'
    | 'dim-2-medium'
    | 'dim-2-semibold'
    | 'dim-2-big'
    | 'dim-3'
    | 'secondary'
    | 'secondary-2xs'
    | 'secondary-medium'
    | 'success'
    | 'success-2xs'
    | 'success-medium'
    | 'white'
    | undefined
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useTextStyles = ({ variant, className }: TextStylesProps) =>
  clsx(
    {
      'text-dim-1 text-xs': variant === 'subtitle',
      'text-2xs text-dim-1': variant === 'dim',
      'text-2xs text-dim-1/80': variant === 'dimmer',
      'text-dim-2 text-xs': variant === 'dim-2',
      'text-dim-2 text-xs font-medium': variant === 'dim-2-medium',
      'text-dim-2 text-xs font-semibold': variant === 'dim-2-semibold',
      'text-dim-2 text-md font-semibold': variant === 'dim-2-big',
      'font-urbanist text-md text-white sm:text-lg xl:text-2xl': variant === 'slide',
      'text-2xs font-semibold text-dim-3': variant === 'dim-3',
      'text-xs text-dim-1 font-semibold': variant === 'dim-xs',
      'text-xs text-secondary': variant === 'secondary',
      'text-2xs text-secondary': variant === 'secondary-2xs',
      'text-xs font-medium text-secondary': variant === 'secondary-medium',
      'text-xs text-success': variant === 'success',
      'text-2xs text-success': variant === 'success-2xs',
      'text-xs font-medium text-success': variant === 'success-medium',
      'text-white text-xs font-medium': variant === 'white',
    },
    className,
  )

export const Text: FC<
  {
    el?: 'p' | 'span' | 'legend' | 'label'
    children: React.ReactNode
    htmlFor?: string | undefined
  } & TextStylesProps
> = ({ el = 'p', variant = 'dim', className, children, ...props }) => {
  const El = el
  const styles = useTextStyles({ variant, className })

  return (
    <El className={styles} {...props}>
      {children}
    </El>
  )
}
